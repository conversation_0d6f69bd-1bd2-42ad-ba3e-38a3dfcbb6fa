import { IsEnum,IsArray ,  IsNotEmpty, IsOptional, IsString, IsUrl, IsNumber } from 'class-validator';
import { LessonContentType } from '../entities/lesson-content-type.enum';
import { CreateQuizDto } from 'src/modules/Quiz/dto/create-quiz.dto';
export class CreateLessonContentDto {
  @IsEnum(LessonContentType)
  type: LessonContentType;

  @IsOptional()
  @IsUrl({}, { message: 'contentURL must be a valid URL' })
  contentURL?: string;

  @IsOptional()
  @IsString()
  text?: string;

  @IsNotEmpty()
  @IsNumber()
  lessonId: number; 
  
  @IsOptional()
  @IsArray()
  quizData?: CreateQuizDto[];

}
