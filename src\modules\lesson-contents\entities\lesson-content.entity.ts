import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn,OneToMany, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { Lesson } from '../../lessons/entities/lesson.entity';
import { LessonContentType } from './lesson-content-type.enum';
import { Quiz } from '../../Quiz/entities/quiz.entity';
import { Assignment } from '../../assignments/entities/assigment.entity'
@Entity('lesson_contents')
export class LessonContent {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: LessonContentType })
  type: LessonContentType;

  @Column({ type: 'text', nullable: true })
  contentURL: string;  

  @Column({ type: 'text', nullable: true })
  text: string;  
  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Lesson, (lesson) => lesson.contents, { onDelete: 'CASCADE' })
  lesson: Lesson;

  @OneToMany(() => Quiz, (quiz) => quiz.content)
  quizzes: Quiz[];

  @OneToMany(() => Assignment, (assignment) => assignment.content)
assignments: Assignment[];


}
