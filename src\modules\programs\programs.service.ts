import { Injectable, NotFoundException } from '@nestjs/common';
import { Program } from './entities/program.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {CreateProgramDto } from './dto/create-program.dto'; 
import { UpdateProgramDto } from './dto/update-program.dto';
 @Injectable()
export class ProgramsService {

    constructor(
        @InjectRepository(Program)
        private   programsRepo: Repository<Program>,
      ) {}

      async create(dto: CreateProgramDto): Promise<Program> {
        const program = this.programsRepo.create(dto);
        return this.programsRepo.save(program);
      }

      async findAll(): Promise<Program[]> { 
          return this.programsRepo.find({ relations: ['courses','courses.lessons','courses.lessons.contents'] });
      }

      async findOne(id: number): Promise<Program> {
        const program = await this.programsRepo.findOne({
          where: { id },
          relations: ['courses'],
        });
        if (!program) throw new NotFoundException('Program not found');
        return program;
      }

      async update(id: number, dto: UpdateProgramDto): Promise<Program> {
        const program = await this.programsRepo.findOne({
          where: { id },
          relations: ['courses'],
        });
        if (!program) throw new NotFoundException('Program not found');

        this.programsRepo.merge(program, dto);
        return this.programsRepo.save(program);
      }
      async delete(id: number): Promise<Program | null> { 
        const program = await this.programsRepo.findOneBy({ id }); 
        if(!program) return null; 
        await this.programsRepo.remove(program);
        return program;
      }
}
