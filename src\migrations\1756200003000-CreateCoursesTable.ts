import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCoursesTable1756200003000 implements MigrationInterface {
  name = 'CreateCoursesTable1756200003000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create courses table
    await queryRunner.query(`
      CREATE TABLE "courses" (
        "id" SERIAL NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text,
        "thumbnailURL" character varying(500),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "instructorId" integer,
        "programId" integer,
        CONSTRAINT "PK_3f70a487cc718ad8eda4e6d58c9" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD CONSTRAINT "FK_courses_instructor" 
      FOREIGN KEY ("instructorId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD CONSTRAINT "FK_courses_program" 
      FOREIGN KEY ("programId") REFERENCES "programs"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_courses_title" ON "courses" ("title")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_instructor" ON "courses" ("instructorId")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_program" ON "courses" ("programId")`);
    await queryRunner.query(`CREATE INDEX "IDX_courses_created_at" ON "courses" ("createdAt")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_courses_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_program"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_instructor"`);
    await queryRunner.query(`DROP INDEX "IDX_courses_title"`);
    
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_program"`);
    await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_instructor"`);
    
    // Drop table
    await queryRunner.query(`DROP TABLE "courses"`);
  }
}
