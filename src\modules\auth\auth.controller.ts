import { <PERSON>, Post, Body, BadRequestException } from '@nestjs/common';
import {AuthService} from './auth.service' ;
import { CreateUserDto } from '../users/dto/create-user.dto';
import { LoginDto } from './dto/login.dto';

@Controller('auth')
export class AuthController {
     constructor(private readonly authService: AuthService) {}
    
     @Post('register')
     async register(@Body() createUserDto : CreateUserDto){  
        try { 
                return await this.authService.register(createUserDto) 
        }
        catch{ 
                throw new BadRequestException('User already exists')
        }
     };

     
     @Post('login')  
     async login(@Body() LoginDto : LoginDto){  
        try { 
                const { email , password } = LoginDto ;
                return await this.authService.login(email , password ) ;  
        }
        catch{ 
                throw new BadRequestException('User not found')
        }
     };
}
