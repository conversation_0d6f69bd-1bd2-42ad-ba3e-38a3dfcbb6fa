import { Module } from '@nestjs/common';
 
import { ProgramsModule } from './modules/programs/programs.module';
import { CoursesModule } from './modules/courses/courses.module';
import { LessonsModule } from './modules/lessons/lessons.module';
import { LessonContentsModule } from './modules/lesson-contents/lesson-contents.module';
import { EnrollmentsModule } from './modules/enrollments/enrollments.module';
import { DatabaseModule } from './database/database.module';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from './modules/users/users.module'; 
import { AuthModule } from './modules/auth/auth.module' ; 
import  { QuizModule } from './modules/Quiz/quiz.module' ; 
import { AssigmentsModule } from './modules/assignments/assigments.module';
 @Module({
  imports: [ProgramsModule, CoursesModule, LessonsModule, LessonContentsModule, EnrollmentsModule , ConfigModule.forRoot({ isGlobal: true }),
    DatabaseModule,UsersModule , AuthModule , QuizModule , AssigmentsModule],
  controllers: [],
  providers: [],
})
export class AppModule {}
