import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Quiz } from './entities/quiz.entity';
import { CreateQuizDto } from './dto/create-quiz.dto';
import { UpdateQuizDto } from './dto/update-quiz.dto';
import { LessonContent } from '../lesson-contents/entities/lesson-content.entity';

@Injectable()
export class QuizService {
  constructor(
    @InjectRepository(Quiz)
    private readonly quizRepo: Repository<Quiz>,
  ) {}

  // create single quiz
  async create(data: CreateQuizDto, content?: LessonContent) {
    if (!data.options || !Array.isArray(data.options)) {
      throw new BadRequestException('Options must be a valid array');
    }

    if (!data.options.includes(data.correctAnswer)) {
      throw new BadRequestException('Correct answer must be one of the options');
    }

    const quiz = this.quizRepo.create({ ...data, content });
    return this.quizRepo.save(quiz);
  }

  // create multiple quizzes for a specific content
  async createBulk(dataArray: CreateQuizDto[], content: LessonContent) {
    if (!dataArray.length) return [];
        const quizzesToSave = dataArray.map(data => {
      if (!data.options || !Array.isArray(data.options)) {
        throw new BadRequestException(`Options must be an array for question: ${data.question}`);
      }

      if (!data.options.includes(data.correctAnswer)) {
        throw new BadRequestException(
          `Correct answer must be one of the options for question: ${data.question}`,
        );
      }
      return this.quizRepo.create({ ...data, content });
    });
    return this.quizRepo.save(quizzesToSave);
  }

  async findAll() {
    return this.quizRepo.find({ relations: ['content'] }); // optionally load content
  }

  async findOne(id: number) {
    const quiz = await this.quizRepo.findOne({ where: { id }, relations: ['content'] });
    if (!quiz) throw new NotFoundException(`Quiz #${id} not found`);
    return quiz;
  }

  async update(id: number, data: UpdateQuizDto) {
    const quiz = await this.findOne(id);
    Object.assign(quiz, data);

    if (quiz.correctAnswer && !quiz.options.includes(quiz.correctAnswer)) {
      throw new BadRequestException('Correct answer must be one of the options');
    }

    return this.quizRepo.save(quiz);
  }

  async remove(id: number) {
    const quiz = await this.findOne(id);
    return this.quizRepo.remove(quiz);
  }
}
