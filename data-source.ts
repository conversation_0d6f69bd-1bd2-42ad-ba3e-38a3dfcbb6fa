import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import { User } from './src/modules/users/entities/user.entity';
import { Course } from './src/modules/courses/entities/course.entity';
import { Lesson } from './src/modules/lessons/entities/lesson.entity';
import { LessonContent } from './src/modules/lesson-contents/entities/lesson-content.entity';
import { Program } from './src/modules/programs/entities/program.entity';
import { Quiz } from './src/modules/Quiz/entities/quiz.entity';
import { Assignment } from './src/modules/assignments/entities/assigment.entity';
import { Video } from './src/modules/videos/entities/video.entity';
dotenv.config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: Number(process.env.DB_PORT),
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  synchronize: false,
  logging: true,
  entities: [User, Course, Lesson, LessonContent, Program, Quiz, Assignment, Video],
  migrations: ['src/migrations/*.ts'],
});
