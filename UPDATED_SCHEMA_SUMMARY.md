# Updated Database Schema Summary

## Changes Made

### 1. **New Video Entity Created**
- **Table**: `videos`
- **Purpose**: Store video-specific information separately from lesson content
- **Fields**:
  - `id` (Primary Key)
  - `title` (VARCHAR 255)
  - `videoURL` (TEXT) - Required
  - `description` (TEXT, nullable)
  - `duration` (INT, nullable) - Duration in seconds
  - `thumbnailURL` (VARCHAR 500, nullable)
  - `createdAt`, `updatedAt` (TIMESTAMP)

### 2. **Relationship Changes: OneToMany → OneToOne**

#### Before (OneToMany):
- LessonContent → Quiz (1:N)
- LessonContent → Assignment (1:N)

#### After (OneToOne):
- LessonContent ↔ Video (1:1)
- LessonContent ↔ Quiz (1:1)
- LessonContent ↔ Assignment (1:1)

### 3. **Updated Entity Relationships**

#### LessonContent Entity:
```typescript
@OneToOne(() => Video, (video) => video.content, { cascade: true })
@JoinColumn()
video: Video;

@OneToOne(() => Quiz, (quiz) => quiz.content, { cascade: true })
@JoinColumn()
quiz: Quiz;

@OneToOne(() => Assignment, (assignment) => assignment.content, { cascade: true })
@JoinColumn()
assignment: Assignment;
```

#### Video Entity:
```typescript
@OneToOne(() => LessonContent, (content) => content.video, { onDelete: 'CASCADE' })
content: LessonContent;
```

#### Quiz Entity:
```typescript
@OneToOne(() => LessonContent, (content) => content.quiz, { onDelete: 'CASCADE' })
content: LessonContent;
```

#### Assignment Entity:
```typescript
@OneToOne(() => LessonContent, (content) => content.assignment, { onDelete: 'CASCADE' })
content: LessonContent;
```

## Migration Files Order

### Existing Migrations:
1. `1756200001000-CreateUsersTable.ts`
2. `1756200002000-CreateProgramsTable.ts`
3. `1756200004000-CreateCoursesTable.ts`
4. `1756200005000-CreateLessonsTable.ts`
5. `1756200006000-CreateLessonContentsTable.ts` *(Updated)*
6. `1756200007000-CreateQuizzesTable.ts` *(Updated - removed contentId)*
7. `1756200008000-CreateAssignmentsTable.ts` *(Updated - removed contentId)*

### New Migrations:
8. `1756200009000-CreateVideosTable.ts`
9. `1756200010000-UpdateLessonContentsForOneToOne.ts`

## Database Schema

### Foreign Key Structure:
```
lesson_contents:
├── lessonId → lessons.id (CASCADE)
├── videoId → videos.id (SET NULL, UNIQUE)
├── quizId → quizzes.id (SET NULL, UNIQUE)
└── assignmentId → assignments.id (SET NULL, UNIQUE)
```

### Cascade Rules:
- **LessonContent → Lesson**: CASCADE DELETE
- **LessonContent → Video**: SET NULL (OneToOne)
- **LessonContent → Quiz**: SET NULL (OneToOne)
- **LessonContent → Assignment**: SET NULL (OneToOne)

## API Changes

### New Video Module:
- **Controller**: `VideosController`
- **Service**: `VideosService`
- **DTOs**: `CreateVideoDto`, `UpdateVideoDto`

### Updated LessonContent DTO:
```typescript
export class CreateLessonContentDto {
  type: LessonContentType;
  contentURL?: string;
  text?: string;
  lessonId: number;
  
  // New OneToOne relationship data
  quizData?: CreateQuizDto;           // Single quiz (not array)
  videoData?: Omit<CreateVideoDto, 'lessonContentId'>;
  assignmentData?: Omit<CreateAssignmentDto, 'lessonContentId'>;
}
```

## Benefits of New Structure

### ✅ **OneToOne Relationships**:
- Each lesson content can have exactly one quiz, one video, or one assignment
- Better data integrity
- Clearer business logic

### ✅ **Separate Video Entity**:
- Dedicated video metadata (duration, thumbnail)
- Better video management
- Specialized video operations

### ✅ **Improved Performance**:
- Unique constraints on foreign keys
- More efficient queries
- Better indexing possibilities

## Usage Examples

### Creating Video Content:
```typescript
const videoContent = {
  type: LessonContentType.VIDEO,
  lessonId: 1,
  videoData: {
    title: "Introduction to TypeScript",
    videoURL: "https://example.com/video.mp4",
    description: "Learn TypeScript basics",
    duration: 1800, // 30 minutes
    thumbnailURL: "https://example.com/thumb.jpg"
  }
};
```

### Creating Quiz Content:
```typescript
const quizContent = {
  type: LessonContentType.QUIZ,
  lessonId: 1,
  quizData: {
    question: "What is TypeScript?",
    options: ["JavaScript", "Superset of JavaScript", "Database"],
    correctAnswer: "Superset of JavaScript"
  }
};
```

## Migration Commands

```bash
# Run all migrations in order
npm run migration:run

# Check migration status
npm run migration:show

# Revert if needed
npm run migration:revert
```

## Notes

1. **Data Migration**: Existing data will need to be migrated if you have existing quiz/assignment records
2. **API Breaking Changes**: The DTO structure has changed from arrays to single objects
3. **Service Updates**: All services have been updated to handle OneToOne relationships
4. **Validation**: Enhanced validation for content type matching

This new structure provides better data integrity, clearer relationships, and more specialized handling for different content types.
