#!/usr/bin/env node

/**
 * Migration Runner Script for NestJS Courses Platform
 * 
 * This script helps you run TypeORM migrations for all entities.
 * Make sure your .env file is properly configured with database credentials.
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 NestJS Courses Platform - Migration Runner');
console.log('============================================\n');

// Check if .env file exists
const fs = require('fs');
if (!fs.existsSync('.env')) {
  console.error('❌ Error: .env file not found!');
  console.log('Please create a .env file with the following variables:');
  console.log('DB_HOST=localhost');
  console.log('DB_PORT=5432');
  console.log('DB_USER=your_username');
  console.log('DB_PASS=your_password');
  console.log('DB_NAME=your_database_name');
  process.exit(1);
}

const commands = {
  'run': 'npx typeorm migration:run -d data-source.ts',
  'revert': 'npx typeorm migration:revert -d data-source.ts',
  'generate': 'npx typeorm migration:generate ./src/migrations/NewMigration -d data-source.ts',
  'show': 'npx typeorm migration:show -d data-source.ts'
};

const action = process.argv[2];

if (!action || !commands[action]) {
  console.log('Usage: node run-migration.js <action>');
  console.log('\nAvailable actions:');
  console.log('  run      - Run pending migrations');
  console.log('  revert   - Revert the last migration');
  console.log('  generate - Generate a new migration');
  console.log('  show     - Show migration status');
  console.log('\nExamples:');
  console.log('  node run-migration.js run');
  console.log('  node run-migration.js revert');
  process.exit(1);
}

try {
  console.log(`📋 Executing: ${action.toUpperCase()}`);
  console.log(`Command: ${commands[action]}\n`);
  
  const result = execSync(commands[action], { 
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  console.log(`\n✅ Migration ${action} completed successfully!`);
  
  if (action === 'run') {
    console.log('\n📊 Your database now includes all tables:');
    console.log('  • users (with UserRole enum)');
    console.log('  • programs');
    console.log('  • courses');
    console.log('  • lessons');
    console.log('  • lesson_contents (with LessonContentType enum)');
    console.log('  • quizzes');
    console.log('  • assignments');
    console.log('\n🔗 All foreign key relationships are properly established.');
  }
  
} catch (error) {
  console.error(`\n❌ Error during migration ${action}:`);
  console.error(error.message);
  process.exit(1);
}
