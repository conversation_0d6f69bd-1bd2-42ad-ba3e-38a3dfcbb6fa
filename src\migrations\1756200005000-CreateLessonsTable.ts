import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateLessonsTable1756200005000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'lessons',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'title',
                        type: 'varchar',
                        length: '255',
                        isNullable: false,
                    },
                    {
                        name: 'description',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'order',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'coverImageURL',
                        type: 'varchar',
                        length: '500',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp',
                        default: 'NOW()',
                    },
                    {
                        name: 'courseId',
                        type: 'int',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // Create foreign key for course
        await queryRunner.createForeignKey(
            'lessons',
            new TableForeignKey({
                columnNames: ['courseId'],
                referencedColumnNames: ['id'],
                referencedTableName: 'courses',
                onDelete: 'CASCADE',
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('lessons');
        
        // Drop foreign key
        const courseForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('courseId') !== -1);
        
        if (courseForeignKey) {
            await queryRunner.dropForeignKey('lessons', courseForeignKey);
        }
        
        await queryRunner.dropTable('lessons');
    }
}
