import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LessonContent } from './entities/lesson-content.entity';
import { CreateLessonContentDto } from './dto/create-lesson-content.dto';
import { UpdateLessonContentDto } from './dto/update-lesson-content.dto';
import { Lesson } from '../lessons/entities/lesson.entity';
import { LessonContentType } from './entities/lesson-content-type.enum';
import { QuizService } from '../Quiz/quiz.service';
@Injectable()
export class LessonContentService {
  constructor(
    @InjectRepository(LessonContent)
    private readonly contentRepo: Repository<LessonContent>,
    @InjectRepository(Lesson)
    private readonly lessonRepo: Repository<Lesson>,
    private readonly quizService: QuizService, // inject QuizService
  ) {}

async create(dto: CreateLessonContentDto) {
  const lesson = await this.lessonRepo.findOne({ where: { id: dto.lessonId } });
  if (!lesson) throw new NotFoundException('Lesson not found');

  // Validation based on type
  if (
    (dto.type === LessonContentType.TEXT && !dto.text) ||
    ([LessonContentType.VIDEO, LessonContentType.PDF, LessonContentType.ASSIGNMENT].includes(dto.type) &&
      !dto.contentURL)
  ) {
    throw new BadRequestException('Content does not match type requirements');
  }

  // Create the LessonContent
  const content = this.contentRepo.create({ ...dto, lesson });
  const savedContent = await this.contentRepo.save(content);

  // Create quizzes if type is QUIZ
  if (dto.type === LessonContentType.QUIZ && dto.quizData?.length) {
    await this.quizService.createBulk(dto.quizData, savedContent);
  }

  // Fetch saved content with relations to return quizzes
  const contentWithRelations = await this.contentRepo.findOne({
    where: { id: savedContent.id },
    relations: ['lesson', 'quizzes', 'assignments'],
  });

  return contentWithRelations;
}


  async findAll() {
    return this.contentRepo.find({ relations: ['lesson', 'lesson.contents', 'assignments'] });
  }

  async findOne(id: number) {
    const content = await this.contentRepo.findOne({
      where: { id },
      relations: ['lesson', 'quizzes', 'assignments'],
    });
    if (!content) throw new NotFoundException('LessonContent not found');
    return content;
  }

  async update(id: number, dto: UpdateLessonContentDto) {
    const content = await this.findOne(id);
    Object.assign(content, dto);

    // Validate after update
    if (
      (content.type === LessonContentType.TEXT && !content.text) ||
      ([LessonContentType.VIDEO, LessonContentType.PDF, LessonContentType.ASSIGNMENT].includes(content.type) &&
        !content.contentURL)
    ) {
      throw new BadRequestException('Content does not match type requirements');
    }

    return this.contentRepo.save(content);
  }

  async remove(id: number) {
    const content = await this.findOne(id);
    return this.contentRepo.remove(content);
  }
}
