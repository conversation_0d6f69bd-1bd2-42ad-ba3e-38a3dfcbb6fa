import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAllTables1756200000000 implements MigrationInterface {
  name = 'CreateAllTables1756200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create UserRole enum
    await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('user', 'instructor', 'admin')`);
    
    // Create LessonContentType enum
    await queryRunner.query(`CREATE TYPE "public"."lesson_contents_type_enum" AS ENUM('video', 'audio', 'pdf', 'text', 'quiz', 'assignment')`);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" SERIAL NOT NULL,
        "name" character varying(100) NOT NULL,
        "email" character varying NOT NULL,
        "passwordHash" character varying NOT NULL,
        "phoneNumber" character varying(20),
        "avatarUrl" character varying,
        "role" "public"."users_role_enum" NOT NULL DEFAULT 'user',
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
        CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
      )
    `);

    // Create programs table
    await queryRunner.query(`
      CREATE TABLE "programs" (
        "id" SERIAL NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text,
        "imgURL" character varying(500),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_d43c664bcaafc0e8a06dfd34e05" PRIMARY KEY ("id")
      )
    `);

    // Create courses table
    await queryRunner.query(`
      CREATE TABLE "courses" (
        "id" SERIAL NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text,
        "thumbnailURL" character varying(500),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "instructorId" integer,
        "programId" integer,
        CONSTRAINT "PK_3f70a487cc718ad8eda4e6d58c9" PRIMARY KEY ("id")
      )
    `);

    // Create lessons table
    await queryRunner.query(`
      CREATE TABLE "lessons" (
        "id" SERIAL NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text,
        "order" integer NOT NULL,
        "coverImageURL" character varying(500),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "courseId" integer,
        CONSTRAINT "PK_9c32c70cb6895b6ef9d39e3e5c1" PRIMARY KEY ("id")
      )
    `);

    // Create lesson_contents table
    await queryRunner.query(`
      CREATE TABLE "lesson_contents" (
        "id" SERIAL NOT NULL,
        "type" "public"."lesson_contents_type_enum" NOT NULL,
        "contentURL" text,
        "text" text,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "lessonId" integer,
        CONSTRAINT "PK_b18b48d72e4e5c8b8c8b8c8b8c8" PRIMARY KEY ("id")
      )
    `);

    // Create quizzes table
    await queryRunner.query(`
      CREATE TABLE "quizzes" (
        "id" SERIAL NOT NULL,
        "question" character varying NOT NULL,
        "options" text NOT NULL,
        "correctAnswer" character varying NOT NULL,
        "contentId" integer,
        CONSTRAINT "PK_b24f0f7662cf6b3a0e7dba0a1b4" PRIMARY KEY ("id")
      )
    `);

    // Create assignments table
    await queryRunner.query(`
      CREATE TABLE "assignments" (
        "id" SERIAL NOT NULL,
        "title" character varying NOT NULL,
        "description" text,
        "fileURL" text,
        "dueDate" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "contentId" integer,
        CONSTRAINT "PK_c54550b8b5d3a4e8b5d3a4e8b5d" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD CONSTRAINT "FK_courses_instructor" 
      FOREIGN KEY ("instructorId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD CONSTRAINT "FK_courses_program" 
      FOREIGN KEY ("programId") REFERENCES "programs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "lessons" 
      ADD CONSTRAINT "FK_lessons_course" 
      FOREIGN KEY ("courseId") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "lesson_contents" 
      ADD CONSTRAINT "FK_lesson_contents_lesson" 
      FOREIGN KEY ("lessonId") REFERENCES "lessons"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "quizzes" 
      ADD CONSTRAINT "FK_quizzes_content" 
      FOREIGN KEY ("contentId") REFERENCES "lesson_contents"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "assignments" 
      ADD CONSTRAINT "FK_assignments_content" 
      FOREIGN KEY ("contentId") REFERENCES "lesson_contents"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints first
    await queryRunner.query(`ALTER TABLE "assignments" DROP CONSTRAINT "FK_assignments_content"`);
    await queryRunner.query(`ALTER TABLE "quizzes" DROP CONSTRAINT "FK_quizzes_content"`);
    await queryRunner.query(`ALTER TABLE "lesson_contents" DROP CONSTRAINT "FK_lesson_contents_lesson"`);
    await queryRunner.query(`ALTER TABLE "lessons" DROP CONSTRAINT "FK_lessons_course"`);
    await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_program"`);
    await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_instructor"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "assignments"`);
    await queryRunner.query(`DROP TABLE "quizzes"`);
    await queryRunner.query(`DROP TABLE "lesson_contents"`);
    await queryRunner.query(`DROP TABLE "lessons"`);
    await queryRunner.query(`DROP TABLE "courses"`);
    await queryRunner.query(`DROP TABLE "programs"`);
    await queryRunner.query(`DROP TABLE "users"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "public"."lesson_contents_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
  }
}
