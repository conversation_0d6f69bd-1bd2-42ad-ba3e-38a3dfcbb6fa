# NestJS Entities Documentation

## Overview
This document provides a comprehensive overview of all entities in the NestJS Courses Platform project, organized by modules.

## Entities by Module

### 1. Users Module
**Entity**: `User`  
**Table**: `users`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `name`: VARCHAR(100), NOT NULL
- `email`: VARCHAR, NOT NULL, UNIQUE
- `passwordHash`: VARCHAR, NOT NULL
- `phoneNumber`: VARCHAR(20), NULLABLE
- `avatarUrl`: VARCHAR, NULLABLE
- `role`: ENUM (UserRole), DEFAULT 'user'
- `createdAt`: TIMESTAMP WITH TIME ZONE, Auto-generated
- `updatedAt`: TIMESTAMP WITH TIME ZONE, Auto-updated

#### Enums:
```typescript
enum UserRole {
  USER = 'user',
  INSTRUCTOR = 'instructor', 
  ADMIN = 'admin'
}
```

#### Relations:
- **OneToMany**: `courses` → Course.instructor

---

### 2. Programs Module
**Entity**: `Program`  
**Table**: `programs`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `title`: VARCHAR(255), NOT NULL
- `description`: TEXT, NULLABLE
- `imgURL`: VARCHAR(500), NULLABLE
- `createdAt`: TIMESTAMP, Auto-generated
- `updatedAt`: TIMESTAMP, Auto-updated

#### Relations:
- **OneToMany**: `courses` → Course.program

---

### 3. Courses Module
**Entity**: `Course`  
**Table**: `courses`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `title`: VARCHAR(255), NOT NULL
- `description`: TEXT, NULLABLE
- `thumbnailURL`: VARCHAR(500), NULLABLE
- `createdAt`: TIMESTAMP, Auto-generated
- `updatedAt`: TIMESTAMP, Auto-updated
- `instructorId` (Foreign Key): INTEGER → users.id
- `programId` (Foreign Key): INTEGER → programs.id

#### Relations:
- **ManyToOne**: `instructor` → User
- **ManyToOne**: `program` → Program
- **OneToMany**: `lessons` → Lesson.course

---

### 4. Lessons Module
**Entity**: `Lesson`  
**Table**: `lessons`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `title`: VARCHAR(255), NOT NULL
- `description`: TEXT, NULLABLE
- `order`: INTEGER, NOT NULL
- `coverImageURL`: VARCHAR(500), NULLABLE
- `createdAt`: TIMESTAMP, Auto-generated
- `updatedAt`: TIMESTAMP, Auto-updated
- `courseId` (Foreign Key): INTEGER → courses.id

#### Relations:
- **ManyToOne**: `course` → Course
- **OneToMany**: `contents` → LessonContent.lesson (CASCADE)

---

### 5. Lesson Contents Module
**Entity**: `LessonContent`  
**Table**: `lesson_contents`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `type`: ENUM (LessonContentType), NOT NULL
- `contentURL`: TEXT, NULLABLE
- `text`: TEXT, NULLABLE
- `createdAt`: TIMESTAMP, Auto-generated
- `lessonId` (Foreign Key): INTEGER → lessons.id

#### Enums:
```typescript
enum LessonContentType {
  VIDEO = 'video',
  AUDIO = 'audio',
  PDF = 'pdf',
  TEXT = 'text',
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment'
}
```

#### Relations:
- **ManyToOne**: `lesson` → Lesson (CASCADE DELETE)
- **OneToMany**: `quizzes` → Quiz.content
- **OneToMany**: `assignments` → Assignment.content

---

### 6. Quiz Module
**Entity**: `Quiz`  
**Table**: `quizzes`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `question`: VARCHAR, NOT NULL
- `options`: TEXT (simple-json), NOT NULL - Array of strings
- `correctAnswer`: VARCHAR, NOT NULL
- `contentId` (Foreign Key): INTEGER → lesson_contents.id

#### Relations:
- **ManyToOne**: `content` → LessonContent (CASCADE DELETE)

---

### 7. Assignments Module
**Entity**: `Assignment`  
**Table**: `assignments`

#### Fields:
- `id` (Primary Key): SERIAL, Auto-generated
- `title`: VARCHAR, NOT NULL
- `description`: TEXT, NULLABLE
- `fileURL`: TEXT, NULLABLE
- `dueDate`: TIMESTAMP, NULLABLE
- `createdAt`: TIMESTAMP, Auto-generated
- `contentId` (Foreign Key): INTEGER → lesson_contents.id

#### Relations:
- **ManyToOne**: `content` → LessonContent (CASCADE DELETE)

---

### 8. Enrollments Module
**Status**: Module exists but no entities defined yet.

## Database Relationships Summary

### Cascade Rules:
1. **LessonContent → Lesson**: CASCADE DELETE
2. **Quiz → LessonContent**: CASCADE DELETE  
3. **Assignment → LessonContent**: CASCADE DELETE
4. **Lesson → LessonContent**: CASCADE (via entity cascade option)

### Foreign Key Constraints:
- `courses.instructorId` → `users.id`
- `courses.programId` → `programs.id`
- `lessons.courseId` → `courses.id`
- `lesson_contents.lessonId` → `lessons.id`
- `quizzes.contentId` → `lesson_contents.id`
- `assignments.contentId` → `lesson_contents.id`

## Migration Commands

To generate and run the migration:

```bash
# Generate migration (if needed)
npx typeorm migration:generate ./src/migrations/CreateAllTables -d data-source.ts

# Run migration
npx typeorm migration:run -d data-source.ts

# Revert migration (if needed)
npx typeorm migration:revert -d data-source.ts
```

## Notes

1. All timestamps use PostgreSQL's `TIMESTAMP` type except User entity which uses `TIMESTAMP WITH TIME ZONE`
2. The Quiz entity stores options as `simple-json` which serializes arrays to JSON
3. Proper cascade deletes are implemented for content-related entities
4. All foreign keys have proper constraints with appropriate delete behaviors
5. Unique constraints are applied where necessary (e.g., user email)
