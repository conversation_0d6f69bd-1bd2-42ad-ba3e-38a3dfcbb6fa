import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateAssignmentsTable1756200008000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'assignments',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'title',
                        type: 'varchar',
                        isNullable: false,
                    },
                    {
                        name: 'description',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'fileURL',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'dueDate',
                        type: 'timestamp',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp',
                        default: 'NOW()',
                    },
                    {
                        name: 'contentId',
                        type: 'int',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // Create foreign key for lesson content
        await queryRunner.createForeignKey(
            'assignments',
            new TableForeignKey({
                columnNames: ['contentId'],
                referencedColumnNames: ['id'],
                referencedTableName: 'lesson_contents',
                onDelete: 'CASCADE',
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('assignments');
        
        // Drop foreign key
        const contentForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('contentId') !== -1);
        
        if (contentForeignKey) {
            await queryRunner.dropForeignKey('assignments', contentForeignKey);
        }
        
        await queryRunner.dropTable('assignments');
    }
}
