import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProgramsTable1756200002000 implements MigrationInterface {
  name = 'CreateProgramsTable1756200002000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create programs table
    await queryRunner.query(`
      CREATE TABLE "programs" (
        "id" SERIAL NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text,
        "imgURL" character varying(500),
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_d43c664bcaafc0e8a06dfd34e05" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_programs_title" ON "programs" ("title")`);
    await queryRunner.query(`CREATE INDEX "IDX_programs_created_at" ON "programs" ("createdAt")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_programs_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_programs_title"`);
    
    // Drop table
    await queryRunner.query(`DROP TABLE "programs"`);
  }
}
