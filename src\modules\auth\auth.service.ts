import { Injectable, UnauthorizedException } from '@nestjs/common';
import {User} from '../users/entities/user.entity';   
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { JwtService } from '@nestjs/jwt';
import { SapDriver } from 'typeorm/browser/platform/BrowserDisabledDriversDummy.js';


@Injectable()
export class AuthService {
      constructor(
        @InjectRepository(User)
        private usersRepo: Repository<User>,
        private jwtService : JwtService
      ) {}
    
      async register( CreateUserDto:  CreateUserDto  ) { 
        const { password , ...rest }  = CreateUserDto; 
        const passwordHash  = await bcrypt.hash(password , 10 ) ; 
        const newUser = this.usersRepo.create({...rest , passwordHash}) ;
        const savedUser = await this.usersRepo.save(newUser) ;

        const payload = {sub:savedUser.id , email:savedUser.email , role :savedUser.role} ; 
        const token = this.jwtService.sign(payload) ; 
        
        return {user:savedUser , token} ;

      }

      async login(email:string , password:string){
        const user = await this.usersRepo.findOneBy({email}) ; 
        if(!user)  throw new UnauthorizedException('invalid credentials');
        const isMatch =  await bcrypt.compare(password , user.passwordHash) ; 
        if(!isMatch) throw new UnauthorizedException('invalid credentials') ; 

        const payload = {sub:user.id , email : user.email  , role : user.role} ; 
        const token = this.jwtService.sign(payload) ; 

        const { passwordHash:_, ...savedUser } = user  ; 
        return {user:savedUser , token} ; 
      } 
}
