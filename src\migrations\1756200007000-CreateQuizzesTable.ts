import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateQuizzesTable1756200007000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'quizzes',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'question',
                        type: 'varchar',
                        isNullable: false,
                    },
                    {
                        name: 'options',
                        type: 'text',
                        isNullable: false,
                        comment: 'JSON array of options',
                    },
                    {
                        name: 'correctAnswer',
                        type: 'varchar',
                        isNullable: false,
                    },
                    {
                        name: 'contentId',
                        type: 'int',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // Create foreign key for lesson content
        await queryRunner.createForeignKey(
            'quizzes',
            new TableForeignKey({
                columnNames: ['contentId'],
                referencedColumnNames: ['id'],
                referencedTableName: 'lesson_contents',
                onDelete: 'CASCADE',
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('quizzes');
        
        // Drop foreign key
        const contentForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('contentId') !== -1);
        
        if (contentForeignKey) {
            await queryRunner.dropForeignKey('quizzes', contentForeignKey);
        }
        
        await queryRunner.dropTable('quizzes');
    }
}
