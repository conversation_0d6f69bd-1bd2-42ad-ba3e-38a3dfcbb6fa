import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>on<PERSON>ontentController } from './lesson-contents.controller';
import { LessonContentService } from './lesson-contents.service';
import { TypeOrmModule } from '@nestjs/typeorm'; 
import { Lesson } from '../lessons/entities/lesson.entity' ; 
import { LessonContent } from './entities/lesson-content.entity'; 
import {Quiz } from '../Quiz/entities/quiz.entity';
import { QuizService } from '../Quiz/quiz.service' ; 
import { Assignment } from '../assignments/entities/assigment.entity';
@Module({
  imports: [TypeOrmModule.forFeature([LessonContent , Lesson , Quiz , Assignment])], 
  controllers: [LessonContentController],
  providers: [LessonContentService , QuizService], 
})
export class LessonContentsModule {}
