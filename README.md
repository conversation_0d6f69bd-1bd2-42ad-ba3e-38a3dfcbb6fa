# NestJS Courses Platform

A comprehensive course management platform built with NestJS, TypeORM, and PostgreSQL.

## Database Setup & Migrations

### Prerequisites
1. PostgreSQL database running
2. `.env` file configured with database credentials:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_username
DB_PASS=your_password
DB_NAME=your_database_name
```

### Migration Commands

#### Using npm scripts (Recommended):
```bash
# Run all pending migrations
npm run migration:run

# Revert the last migration
npm run migration:revert

# Generate a new migration based on entity changes
npm run migration:generate

# Show migration status
npm run migration:show

# Create a new empty migration file
npm run migration:create
```

#### Using the helper script:
```bash
# Run migrations
node run-migration.js run

# Revert migrations
node run-migration.js revert

# Show migration status
node run-migration.js show
```

#### Direct TypeORM commands:
```bash
# Run migrations
npx typeorm migration:run -d data-source.ts

# Revert migrations
npx typeorm migration:revert -d data-source.ts

# Generate new migration
npx typeorm migration:generate ./src/migrations/NewMigration -d data-source.ts

# Show migration status
npx typeorm migration:show -d data-source.ts
```

### Database Schema

The platform includes the following entities:

1. **Users** - User accounts with roles (user, instructor, admin)
2. **Programs** - Course programs/categories
3. **Courses** - Individual courses within programs
4. **Lessons** - Lessons within courses
5. **Lesson Contents** - Content items within lessons (video, audio, pdf, text, quiz, assignment)
6. **Quizzes** - Quiz questions linked to lesson content
7. **Assignments** - Assignment tasks linked to lesson content

### Entity Relationships

- Users can be instructors of multiple courses
- Programs contain multiple courses
- Courses contain multiple lessons
- Lessons contain multiple content items
- Content items can have associated quizzes and assignments

For detailed entity documentation, see [ENTITIES_DOCUMENTATION.md](./ENTITIES_DOCUMENTATION.md).

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run start:dev

# Run tests
npm test

# Build for production
npm run build
```
