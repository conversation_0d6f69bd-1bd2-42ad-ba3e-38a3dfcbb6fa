import { Controller, Param, ParseIntPipe } from '@nestjs/common';
import { CoursesService } from './courses.service';
import {Get , Post , Put, Delete  , Body} from '@nestjs/common' ; 
import { Course } from './entities/course.entity';
import { CreateCourseDto } from './dto/cousres.dto';
import { UpdateCourseDto } from './dto/update-course.dto'; 
@Controller('courses')
export class CoursesController {

    constructor( private readonly coursesService : CoursesService ) {}

    @Get('/')
    async findAll(): Promise<Course[]> { 
        return this.coursesService.findAll();
    }

    @Get(':id')  
    async findOne(@Param('id' , ParseIntPipe)  id : number): Promise <Course | null> { 
        return this.coursesService.findOne(id);
    }
    
    @Post('/') 
    async create(@Body() createCourseDto : CreateCourseDto) : Promise<Course> { 
        return this.coursesService.create(createCourseDto);
    }
    @Put(':id') 
    async update(@Param('id' , ParseIntPipe) id : number , @Body() updateCourseDto : UpdateCourseDto) : Promise<Course> { 
        return this.coursesService.update(id , updateCourseDto);
    }
    @Delete(':id') 
    async delete(@Param('id' , ParseIntPipe) id : number) : Promise<Course | null> { 
        return this.coursesService.delete(id);
    }
}
