import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Lesson } from './entities/lesson.entity';
import { CreateLessonDto } from './dto/create-lesson.dto';
import { UpdateLessonDto } from './dto/update-lesson.dto';
import { Course } from '../courses/entities/course.entity';

@Injectable()
export class LessonsService {
  constructor(
    @InjectRepository(Lesson)
    private readonly lessonRepo: Repository<Lesson>,

    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,
  ) {}

  // Create a new lesson linked to a course
  async create(dto: CreateLessonDto): Promise<Lesson> {
    const course = await this.courseRepo.findOne({ where: { id: dto.courseId } });
    if (!course) throw new NotFoundException('Course not found');

    const lesson = this.lessonRepo.create({
      ...dto,
      course,  
    });

    return this.lessonRepo.save(lesson);
  }

  // Get all lessons with course and contents
  async findAll(): Promise<Lesson[]> {
    return this.lessonRepo.find({
          relations: [
      'course',
      'contents',
      'contents.quizzes',      
      'contents.assignments', 
    ],
      order: { order: 'ASC' },
    });
  }

  // Get one lesson by ID with course and contents
  async findOne(id: number): Promise<Lesson> {
    const lesson = await this.lessonRepo.findOne({
      where: { id },
      relations: ['course', 'contents'],
    });
    if (!lesson) throw new NotFoundException('Lesson not found');
    return lesson;
  }

  // Update lesson by ID
  async update(id: number, dto: UpdateLessonDto): Promise<Lesson> {
    const lesson = await this.lessonRepo.findOne({ where: { id } });
    if (!lesson) throw new NotFoundException('Lesson not found');

     if (dto.courseId) {
      const course = await this.courseRepo.findOne({ where: { id: dto.courseId } });
      if (!course) throw new NotFoundException('Course not found');
      lesson.course = course;
    }

    this.lessonRepo.merge(lesson, dto);
    return this.lessonRepo.save(lesson);
  }

  // Delete lesson by ID
  async delete(id: number): Promise<Lesson> {
    const lesson = await this.lessonRepo.findOne({
      where: { id },
      relations: ['contents'],
    });
    if (!lesson) throw new NotFoundException('Lesson not found');

    await this.lessonRepo.remove(lesson);
    return lesson;
  }
}
