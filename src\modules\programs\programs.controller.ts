import { <PERSON>, Get, Post, Patch, Delete, Param, Body, ParseIntPipe } from '@nestjs/common';
import { ProgramsService } from './programs.service';
import { CreateProgramDto } from './dto/create-program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';
import { Program } from './entities/program.entity';

@Controller('programs')
export class ProgramsController {
  constructor(private readonly programsService: ProgramsService) {}

  // Create a new program
  @Post()
  async create(@Body() createProgramDto: CreateProgramDto): Promise<Program> {
    return this.programsService.create(createProgramDto);
  }

  // Get all programs
  @Get()
  async findAll(): Promise<Program[]> {
    return this.programsService.findAll();
  }

  // Get a single program by ID
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Program | null > {
    return this.programsService.findOne(id);
  }

  // Update a program by ID
  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProgramDto: UpdateProgramDto,
  ): Promise<Program> {
    return this.programsService.update(id, updateProgramDto);
  }

  // Delete a program by ID
  @Delete(':id')
  async delete(@Param('id', ParseIntPipe) id: number): Promise<Program | null> {
    return this.programsService.delete(id);
  }
}
