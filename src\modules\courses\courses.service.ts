import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Course } from './entities/course.entity';
import { CreateCourseDto } from './dto/cousres.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Program } from '../programs/entities/program.entity'; 

@Injectable()
export class CoursesService {
  constructor(
    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,

    @InjectRepository(Program)  
    private readonly programRepo: Repository<Program>,
  ) {}

  async create(dto: CreateCourseDto): Promise<Course> {
    let program: Program | null = null;

    if (dto.programId) {
      program = await this.programRepo.findOne({ where: { id: dto.programId } });
      if (!program) throw new NotFoundException('Program not found');
    }

    const course = this.courseRepo.create({
      title: dto.title,
      description: dto.description,
      instructor: { id: dto.instructorId },
      program: program ?? undefined,   
      thumbnailURL: dto.thumbnailURL,
    });


    return this.courseRepo.save(course);
  }

  async findAll(): Promise<Course[]> {
    return this.courseRepo.find({
      relations: ['lessons', 'lessons.contents', 'program', 'instructor'], 
    });
  }

  async findOne(id: number): Promise<Course> {
    const course = await this.courseRepo.findOne({
      where: { id },
      relations: ['lessons', 'lessons.contents', 'program', 'instructor'],
    });

    if (!course) throw new NotFoundException(`Course with ID ${id} not found`);
    return course;
  }
  
    async update(id: number, dto: UpdateCourseDto): Promise<Course> {
      const course = await this.courseRepo.findOne({ where: { id } });
      if (!course) throw new NotFoundException('Course not found');

      let program: Program | null = course.program;
      if (dto.programId) {
        program = await this.programRepo.findOne({ where: { id: dto.programId } });
        if (!program) throw new NotFoundException('Program not found');
      }

      this.courseRepo.merge(course, {
        ...dto,
        instructor: dto.instructorId ? { id: dto.instructorId } : course.instructor,
        program: program ?? undefined, 
      });

      return this.courseRepo.save(course);
    }

  async delete(id: number): Promise<Course> {
    const course = await this.courseRepo.findOne({ where: { id } });
    if (!course) throw new NotFoundException('Course not found');

    await this.courseRepo.remove(course);
    return course;
  }
}
