import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { LessonContent } from '../../lesson-contents/entities/lesson-content.entity';

@Entity('quizzes')
export class Quiz {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  question: string;

  @Column('simple-json') 
  options: string[];  

  @Column()
  correctAnswer: string;

  @ManyToOne(() => LessonContent, (content) => content.quizzes, { onDelete: 'CASCADE' })
  content: LessonContent;
}
