import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToOne } from 'typeorm';
import { LessonContent } from '../../lesson-contents/entities/lesson-content.entity';

@Entity('quizzes')
export class Quiz {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  question: string;

  @Column('simple-json')
  options: string[];

  @Column()
  correctAnswer: string;

  @OneToOne(() => LessonContent, (content) => content.quiz, { onDelete: 'CASCADE' })
  content: LessonContent;
}
