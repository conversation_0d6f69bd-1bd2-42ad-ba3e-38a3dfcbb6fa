import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { AssignmentService } from './assigments.service';
import { CreateAssignmentDto } from './dto/create-assigment.dto';
import { UpdateAssigmentDto } from './dto/update-assigment.dto';

@Controller('assignments')
export class AssignmentsController {
  constructor(private readonly assignmentsService: AssignmentService) {}

  @Post()
  async create(@Body() createAssignmentDto: CreateAssignmentDto) {
    return this.assignmentsService.create(createAssignmentDto); 
  }

  @Get()
  async findAll() {
    return this.assignmentsService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.assignmentsService.findOne(+id);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateAssignmentDto: UpdateAssigmentDto) {
    return this.assignmentsService.update(+id, updateAssignmentDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.assignmentsService.remove(+id);
  }
}
