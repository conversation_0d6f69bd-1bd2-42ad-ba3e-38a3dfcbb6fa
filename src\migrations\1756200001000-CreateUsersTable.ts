import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUsersTable1756200001000 implements MigrationInterface {
  name = 'CreateUsersTable1756200001000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create UserRole enum
    await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('user', 'instructor', 'admin')`);
    
    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" SERIAL NOT NULL,
        "name" character varying(100) NOT NULL,
        "email" character varying NOT NULL,
        "passwordHash" character varying NOT NULL,
        "phoneNumber" character varying(20),
        "avatarUrl" character varying,
        "role" "public"."users_role_enum" NOT NULL DEFAULT 'user',
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
        CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_users_email" ON "users" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_users_role" ON "users" ("role")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_users_role"`);
    await queryRunner.query(`DROP INDEX "IDX_users_email"`);
    
    // Drop table
    await queryRunner.query(`DROP TABLE "users"`);
    
    // Drop enum
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
  }
}
